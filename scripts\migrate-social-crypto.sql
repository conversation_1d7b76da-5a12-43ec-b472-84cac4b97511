-- Migration script to convert individual social media and crypto columns to JSON arrays
-- This script should be run on your Supabase database

-- Step 1: Add new columns for social and crypto JSON data
ALTER TABLE contact 
ADD COLUMN IF NOT EXISTS social JSONB,
ADD COLUMN IF NOT EXISTS crypto JSONB;

-- Step 2: Migrate existing social media data to JSON format
UPDATE contact 
SET social = jsonb_build_object(
  'twitter', COALESCE(twitter, ''),
  'telegram', COALESCE(telegram, ''),
  'youtube', COALESCE(youtube, ''),
  'instagram', COALESCE(instagram, ''),
  'facebook', COALESCE(facebook, '')
)
WHERE twitter IS NOT NULL 
   OR telegram IS NOT NULL 
   OR youtube IS NOT NULL 
   OR instagram IS NOT NULL 
   OR facebook IS NOT NULL;

-- Step 3: Migrate existing crypto data to JSON format  
UPDATE contact 
SET crypto = jsonb_build_object(
  'eth', COALESCE(eth, ''),
  'bsc', COALESCE(bsc, ''),
  'matic', COALESCE(matic, ''),
  'btc', COALESCE(btc, ''),
  'fil', COALESCE(fil, ''),
  'sol', COALESCE(sol, '')
)
WHERE eth IS NOT NULL 
   OR bsc IS NOT NULL 
   OR matic IS NOT NULL 
   OR btc IS NOT NULL 
   OR fil IS NOT NULL 
   OR sol IS NOT NULL;

-- Step 4: Clean up empty JSON objects (optional)
UPDATE contact 
SET social = NULL 
WHERE social = '{}'::jsonb 
   OR social = '{"twitter":"","telegram":"","youtube":"","instagram":"","facebook":""}'::jsonb;

UPDATE contact 
SET crypto = NULL 
WHERE crypto = '{}'::jsonb 
   OR crypto = '{"eth":"","bsc":"","matic":"","btc":"","fil":"","sol":""}'::jsonb;

-- Step 5: (Optional) Drop old columns after confirming migration worked
-- WARNING: Only run these after confirming the migration worked correctly!
-- ALTER TABLE contact DROP COLUMN IF EXISTS twitter;
-- ALTER TABLE contact DROP COLUMN IF EXISTS telegram;
-- ALTER TABLE contact DROP COLUMN IF EXISTS youtube;
-- ALTER TABLE contact DROP COLUMN IF EXISTS instagram;
-- ALTER TABLE contact DROP COLUMN IF EXISTS facebook;
-- ALTER TABLE contact DROP COLUMN IF EXISTS eth;
-- ALTER TABLE contact DROP COLUMN IF EXISTS bsc;
-- ALTER TABLE contact DROP COLUMN IF EXISTS matic;
-- ALTER TABLE contact DROP COLUMN IF EXISTS btc;
-- ALTER TABLE contact DROP COLUMN IF EXISTS fil;
-- ALTER TABLE contact DROP COLUMN IF EXISTS sol;

-- Step 6: Add indexes for better performance (optional)
CREATE INDEX IF NOT EXISTS idx_contact_social ON contact USING GIN (social);
CREATE INDEX IF NOT EXISTS idx_contact_crypto ON contact USING GIN (crypto);

-- Verification queries to check the migration
-- SELECT name, social, crypto FROM contact WHERE social IS NOT NULL OR crypto IS NOT NULL LIMIT 10;
-- SELECT COUNT(*) as total_contacts, 
--        COUNT(social) as contacts_with_social, 
--        COUNT(crypto) as contacts_with_crypto 
-- FROM contact;
