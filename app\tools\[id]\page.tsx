"use client"

import { Card } from "src/components/Card"
import { Page } from "src/components/Page"

import { useEffect, useState } from "react"
import { getSupabaseClient } from "src/lib/supabase"
import { Text, Loader, SimpleGrid, Stack } from "@mantine/core"
import { useParams } from "next/navigation"
import { ContactCard } from "src/components/Card/contact"
import { useFooter } from "src/providers/FooterProvider"
import { ToolsActionsGrid } from "src/components/Card/ToolsActionsGrid"
import { useAuthRedirect } from "src/hooks/useAuthRedirect"

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: string | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: Record<string, string> | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  minted: string | null;
}

export default function ProfilePage() {
  const [contact, setContact] = useState<ContactItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [contactRefreshTrigger, setContactRefreshTrigger] = useState(0);
  const [enableModify, setEnableModify] = useState(false);
  const params = useParams();
  const { clearFooterContent } = useFooter();
  const { session, status, isLoading } = useAuthRedirect();
  // Handle both encoded and unencoded URLs, convert to lowercase
  const rawId = params?.id as string;
  const contactName = rawId && rawId.includes('%') ? decodeURIComponent(rawId).toLowerCase() : rawId?.toLowerCase();

  // Function to refresh contact data (for ContactCard)
  const refreshContactData = () => {
    setContactRefreshTrigger(prev => prev + 1);
  };

  useEffect(() => {
    // Don't proceed if still loading authentication
    if (isLoading) {
      return;
    }

    // If not authenticated, the useAuthRedirect hook will handle the redirect
    if (!session) {
      return;
    }

    if (!contactName) {
      setError('Invalid contact name');
      setLoading(false);
      return;
    }

    const fetchContactData = async () => {
      try {
        console.log('Fetching contact:', contactName);
        console.log('Raw params.id:', params?.id);
        console.log('Decoded contactName:', contactName);

        const client = getSupabaseClient();
        const { data, error } = await client
          .from('contact')
          .select('*')
          .eq('name', contactName)
          .single();

        console.log('Contact query results:', { data, error });

        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }

        if (data) {
          //console.log('Contact found:', data);
          // Handle links and images as JSON objects
          const processedData = {
            ...data,
            links: data.links ? (typeof data.links === 'string' ? JSON.parse(data.links) : data.links) : null,
            images: data.images ? (typeof data.images === 'string' ? JSON.parse(data.images) : data.images) : null
          };
          setContact(processedData);

          // Check if the current user owns this contact
          const sessionEmail = session?.user?.email;
        //  console.log('Session email:', sessionEmail);
         // console.log('Contact email:', data.profile_email);
          if (sessionEmail && data.profile_email === sessionEmail) {
            console.log('User is the owner of this contact');
            setEnableModify(true);
          } else {
            console.log('User is not the owner of this contact');
            setEnableModify(false);
          }
        } else {
          console.log('No contact data returned');
          setError(`Contact not found: ${contactName}. Check if name matches exactly.`);
        }
      } catch (error) {
        console.error('Error fetching contact data:', error);
        setError(`Failed to fetch contact data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchContactData();
  }, [contactName, session, isLoading]);

  // Clear footer content when component unmounts
  useEffect(() => {
    return () => {
      clearFooterContent();
    };
  }, []); // Empty dependency array for cleanup only

  // Show loading while authentication is being checked
  if (isLoading) {
    return (
      <Page>
        <Card>
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        </Card>
      </Page>
    );
  }

  // If not authenticated, the useAuthRedirect hook will handle the redirect
  if (!session) {
    return null;
  }

  // If user is not authorized to modify this contact
  if (contact && !enableModify) {
    return (
      <Page>
        <Card>
          <Text ta="center" size="lg" fw={500}>
            You are not authorized to access this contact's tools
          </Text>
          <Text ta="center" size="sm" c="dimmed" mt="md">
            Only the contact owner can access the tools page
          </Text>
        </Card>
      </Page>
    );
  }

  return (
    <Page>
      <Card>
        {error && (
          <Text c="red" ta="center" mb="md">
            {error}
          </Text>
        )}

        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        ) : contact ? (
          <Stack gap="md" mt="xl">
            <SimpleGrid
              cols={{ base: 1, md: 2, lg: 2 }}
              spacing={{ base: 10, sm: 'xs' }}
              verticalSpacing={{ base: 'md', sm: 'xl' }}
            >
              <ContactCard key={contactRefreshTrigger} name={contact.name} />
              <ToolsActionsGrid contactName={contact.name} onContactCreated={refreshContactData} />
            </SimpleGrid>
          </Stack>
        ) : (
          <div style={{ textAlign: 'center' }}>
            <Text c="dimmed" mb="md">
              Contact not found: {contactName}
            </Text>
            <Text size="sm" c="dimmed" mb="md">
              This contact doesn't exist in the database yet.
            </Text>
          </div>
        )}
      </Card>
    </Page>
  );
}
