# Social Media & Crypto Address Migration Guide

This guide explains the migration from individual columns to JSON arrays for social media handles and cryptocurrency addresses.

## Overview

Previously, social media handles and crypto addresses were stored in individual columns:
- Social: `twitter`, `telegram`, `youtube`, `instagram`, `facebook`
- Crypto: `eth`, `bsc`, `matic`, `btc`, `fil`, `sol`

Now they are stored in JSON arrays:
- Social: `social` (JSONB column)
- Crypto: `crypto` (JSONB column)

## Benefits

1. **Easy to add new platforms**: Just add them to the config file
2. **No database schema changes**: New social platforms or crypto currencies don't require ALTER TABLE
3. **Cleaner code**: Dynamic rendering based on configuration
4. **Better maintainability**: Centralized configuration

## Configuration

Social media platforms and crypto currencies are now configured in `src/lib/config.ts`:

```typescript
// Social Media Configuration
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord"];

// Cryptocurrency Configuration  
export const CRYPTO_SLOTS = ["eth", "bsc", "matic", "btc", "fil", "sol"];
```

To add a new platform (e.g., Discord), simply add it to the appropriate array.

## Database Migration

### Automatic Migration (Recommended)

The application now handles both old and new formats automatically using utility functions in `src/lib/database-utils.ts`. This means:

1. **Backward compatibility**: Existing data still works
2. **Gradual migration**: Data is migrated when updated
3. **No downtime**: No need to stop the application

### Manual Migration (Optional)

If you want to migrate all data at once, run the SQL script:

```bash
# Run the migration script on your Supabase database
psql -h your-host -U your-user -d your-database -f scripts/migrate-social-crypto.sql
```

## Code Changes

### Database Types

Updated `src/lib/database.types.ts` to include new columns:
```typescript
social: Json | null
crypto: Json | null
```

### Utility Functions

New utility functions in `src/lib/database-utils.ts`:
- `getSocialData()` - Get social data from either format
- `getCryptoData()` - Get crypto data from either format
- `migrateSocialData()` - Convert legacy to new format
- `migrateCryptoData()` - Convert legacy to new format
- `prepareContactData()` - Prepare data for database operations

### Component Updates

All components now use the new utility functions:
- `app/profile/[id]/page.tsx` - Dynamic social/crypto rendering
- `app/update/page.tsx` - Dynamic form fields
- `src/components/Buttons/AddToMobileContactButton.tsx` - Updated vCard generation
- `app/api/[name]/route.ts` - Updated API response

## Adding New Platforms

### Example: Adding Discord

1. **Update config**:
```typescript
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord"];
```

2. **Add icon support** (in components that need it):
```typescript
case 'discord':
  return <IconBrandDiscord size={30} />;
```

3. **Add validation** (optional):
```typescript
case 'discord':
  // Add Discord-specific validation
  break;
```

That's it! The platform will automatically appear in forms and displays.

## Data Format

### New JSON Structure

```json
{
  "social": {
    "twitter": "username",
    "telegram": "username",
    "discord": "username#1234"
  },
  "crypto": {
    "eth": "0x1234...",
    "btc": "**********************************"
  }
}
```

### Legacy Format (Still Supported)

Individual columns are still read and automatically converted:
```sql
twitter: "username"
telegram: "username"  
eth: "0x1234..."
btc: "**********************************"
```

## Testing

1. **Verify existing data**: Check that existing contacts still display correctly
2. **Test new entries**: Create new contacts and verify they use the new format
3. **Test updates**: Update existing contacts and verify migration
4. **Test new platforms**: Add a new platform to config and test

## Rollback Plan

If needed, you can rollback by:

1. **Restore old components**: Use git to restore previous component versions
2. **Keep both formats**: The utility functions support both formats
3. **Database rollback**: The old columns are preserved during migration

## Support

The migration maintains full backward compatibility. If you encounter issues:

1. Check that utility functions are imported correctly
2. Verify configuration in `src/lib/config.ts`
3. Test with both old and new data formats
4. Check browser console for any errors

## Performance Notes

- JSON columns are indexed with GIN indexes for better performance
- Utility functions cache results where possible
- No performance impact on existing queries
