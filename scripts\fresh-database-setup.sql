-- Fresh database setup script for new social and crypto JSON structure
-- This script creates the contact table with only the new JSON columns

-- Drop the existing contact table if it exists (for fresh start)
DROP TABLE IF EXISTS contact CASCADE;

-- Create the contact table with new structure
CREATE TABLE contact (
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  name TEXT PRIMARY KEY,
  image TEXT,
  description TEXT,
  uri TEXT,
  profile TEXT,
  email TEXT,
  website TEXT,
  phone TEXT,
  tg_bot TEXT,
  notes TEXT,
  web2 TEXT,
  web3 TEXT,
  links JSONB,
  images JSONB,
  social JSONB,
  crypto JSONB,
  profile_email TEXT,
  minted TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_name ON contact (name);
CREATE INDEX IF NOT EXISTS idx_contact_profile_email ON contact (profile_email);
CREATE INDEX IF NOT EXISTS idx_contact_social ON contact USING GIN (social);
CREATE INDEX IF NOT EXISTS idx_contact_crypto ON contact USING GIN (crypto);
CREATE INDEX IF NOT EXISTS idx_contact_links ON contact USING GIN (links);
CREATE INDEX IF NOT EXISTS idx_contact_images ON contact USING GIN (images);

-- Add comments for documentation
COMMENT ON TABLE contact IS 'Contact information with JSON-based social media and crypto data';
COMMENT ON COLUMN contact.social IS 'JSON object containing social media handles: {"twitter": "handle", "telegram": "handle", etc.}';
COMMENT ON COLUMN contact.crypto IS 'JSON object containing cryptocurrency addresses: {"eth": "0x...", "btc": "1...", etc.}';
COMMENT ON COLUMN contact.links IS 'JSON object containing custom links: {"name": "url", etc.}';
COMMENT ON COLUMN contact.images IS 'JSON object containing image URLs: {"1": "url", "2": "url", etc.}';

-- Insert sample data to test the new structure
INSERT INTO contact (
  name,
  profile,
  description,
  email,
  website,
  phone,
  social,
  crypto,
  links,
  profile_email
) VALUES (
  'john.me',
  'John Doe',
  'Software Developer',
  '<EMAIL>',
  'https://johndoe.com',
  '+1234567890',
  '{"twitter": "johndoe", "telegram": "johndoe", "linkedin": "johndoe"}',
  '{"eth": "******************************************", "btc": "**********************************"}',
  '{"Portfolio": "https://portfolio.johndoe.com", "Blog": "https://blog.johndoe.com"}',
  '<EMAIL>'
);

-- Verification queries
SELECT 'Contact table created successfully' as status;
SELECT COUNT(*) as total_contacts FROM contact;
SELECT name, social, crypto FROM contact WHERE social IS NOT NULL OR crypto IS NOT NULL;
