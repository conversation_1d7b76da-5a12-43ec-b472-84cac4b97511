'use client';

import { IconArrowRight, IconSearch } from '@tabler/icons-react';
import { ActionIcon, TextInput, TextInputProps, useMantineTheme } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { isValidODudeName, fetchDomain } from '../../lib/common';
import { useState, useEffect } from 'react';
import { fetchData, insertData } from '../../lib/supabase';
import { useRouter, usePathname } from 'next/navigation';

export async function insertContactData(contact: any) {
  try {
    const insertResult = await insertData('contact', contact);
    if (insertResult.error) {
      console.error('Insert failed:', insertResult.error);
    } else {
      console.log('Record inserted successfully:', insertResult.data);
    }
    return insertResult;
  } catch (error) {
    console.error('Error inserting contact data:', error);
    throw error;
  }
}

// Helper function to prepare contact data for database
export const prepareContactData = (data: any) => {
  const records = Object.values(data.records || {}).reduce((acc: any, record: any) => {
    if (record.type && record.value) {
      acc[record.type] = record.value;
    }
    return acc;
  }, {}) as any;

  return {
    name: data.name,
    description: data.description || '',
    image: data.image || '',
    uri: records.web_url || '',
    profile: records.name || '',
    email: records.email || '',
    website: records.website || '',
    phone: records.phone || '',
    tg_bot: records.tg_bot || '',
    notes: records.notes || '',
    eth: records.crypto?.eth || '',
    bsc: records.crypto?.bsc || '',
    matic: records.crypto?.matic || '',
    btc: records.crypto?.btc || '',
    fil: records.crypto?.fil || '',
    sol: records.crypto?.sol || '',
    twitter: records.social?.twitter || '',
    telegram: records.social?.telegram || '',
    youtube: records.social?.youtube || '',
    instagram: records.social?.instagram || '',
    facebook: records.social?.facebook || '',
    social: records.social || null,
    crypto: records.crypto || null,
    web2: records.web_url || '',
    web3: records.web3_url || '',
    links: records.link || null,  // Add this line
    images: records.img || null    // Add this line
  };
};

export function ODudeNameSearch(props: TextInputProps) {
  const theme = useMantineTheme();
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Clear search input when URL changes (navigation occurs)
  useEffect(() => {
    setInputValue('');
  }, [pathname]);



  const handleValidation = async () => {
    const searchValue = inputValue.toLowerCase().trim();
    if (isValidODudeName(searchValue)) {
      setLoading(true);
      try {
        // First, check if the contact exists in the database
        const existingRecord = await fetchData('contact', {
          filter: [{ column: 'name', value: searchValue }],
          single: true
        });

        let result;

        if (existingRecord.data) {
          // Contact found in database - use database record
       //   console.log('Contact found in database:', existingRecord.data);
          result = existingRecord.data;
          notifications.show({
            title: 'Notice',
            message: 'Contact loading..',
            color: 'blue',
          });
        } else {
          // Contact not found in database - fetch from domain
          console.log('Contact not found in database, fetching from domain...');
          result = await fetchDomain(searchValue);

          if (result.error) {
            // Handle error case
            notifications.show({
              title: 'Error',
              message: result.error,
              color: 'red',
            });
            setLoading(false);
            return;
          }

          // Save new data to Supabase
          try {
            // Prepare data for database
            const contact = prepareContactData(result);
            // Ensure name is lowercase
            contact.name = contact.name.toLowerCase();

            // Insert new record
            const insertResult = await insertContactData(contact);
            if (insertResult.error) {
              console.error('Insert failed:', insertResult.error);
            } else {
              console.log('Record inserted successfully:', insertResult.data);
              notifications.show({
                title: 'Success',
                message: 'Record saved successfully',
                color: 'green',
              });
            }
          } catch (dbError) {
            console.error('Database operation failed:', dbError);
            notifications.show({
              title: 'Database Error',
              message: 'Failed to save record to database',
              color: 'red',
            });
          }
        }

        console.log('Final result:', result);

        // Redirect to profile page using lowercase name
        router.push(`/profile/${result.name.toLowerCase()}`);
      } catch (error) {
        console.error('Error in handleValidation:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to fetch contact information',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    } else {
      console.log('Invalid ODude Name');
      notifications.show({
        title: 'Invalid Input',
        message: 'Please enter a valid ODude Name',
        color: 'yellow',
      });
    }
  };



  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !loading) {
      handleValidation();
    }
  };

  return (
    <TextInput
      radius="xl"
      size="md"
      placeholder="Search Name.."
      value={inputValue}
      onChange={(event) => setInputValue(event.currentTarget.value)}
      onKeyDown={handleKeyDown}
      rightSectionWidth={42}
      leftSection={<IconSearch size={18} stroke={1.5} />}
      rightSection={
        <ActionIcon
          size={32}
          radius="xl"
          color={theme.primaryColor}
          variant="filled"
          onClick={handleValidation}
          loading={loading}
        >
          {!loading && <IconArrowRight size={18} stroke={1.5} />}
        </ActionIcon>
      }
      {...props}
    />
  );
}
