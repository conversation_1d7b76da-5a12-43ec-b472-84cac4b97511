/**
 * Tests for database utility functions
 * Run with: npm test or jest
 */

import {
  migrateSocialData,
  migrateCryptoData,
  getSocialData,
  getCryptoData,
  prepareContactData,
  hasSocialData,
  hasCryptoData,
  getSocialUrl,
  validateS<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateCryptoAddress
} from '../database-utils';

// Mock data for testing
const legacyContactData = {
  name: 'test',
  twitter: 'testuser',
  telegram: 'testuser',
  youtube: '',
  instagram: null,
  facebook: 'testuser',
  eth: '******************************************',
  bsc: '******************************************',
  matic: '',
  btc: null,
  fil: 'f1234567890123456789012345678901234567890',
  sol: ''
};

const newFormatContactData = {
  name: 'test',
  social: {
    twitter: 'testuser',
    telegram: 'testuser',
    discord: 'testuser#1234'
  },
  crypto: {
    eth: '******************************************',
    btc: '**********************************'
  }
};

describe('Database Utils', () => {
  describe('migrateSocialData', () => {
    it('should migrate legacy social data to new format', () => {
      const result = migrateSocialData(legacyContactData);
      expect(result).toEqual({
        twitter: 'testuser',
        telegram: 'testuser',
        facebook: 'testuser'
      });
    });

    it('should handle empty/null values', () => {
      const result = migrateSocialData({});
      expect(result).toEqual({});
    });
  });

  describe('migrateCryptoData', () => {
    it('should migrate legacy crypto data to new format', () => {
      const result = migrateCryptoData(legacyContactData);
      expect(result).toEqual({
        eth: '******************************************',
        bsc: '******************************************',
        fil: 'f1234567890123456789012345678901234567890'
      });
    });
  });

  describe('getSocialData', () => {
    it('should return new format when available', () => {
      const result = getSocialData(newFormatContactData);
      expect(result).toEqual(newFormatContactData.social);
    });

    it('should migrate legacy format when new format not available', () => {
      const result = getSocialData(legacyContactData);
      expect(result.twitter).toBe('testuser');
      expect(result.telegram).toBe('testuser');
      expect(result.facebook).toBe('testuser');
    });
  });

  describe('getCryptoData', () => {
    it('should return new format when available', () => {
      const result = getCryptoData(newFormatContactData);
      expect(result).toEqual(newFormatContactData.crypto);
    });

    it('should migrate legacy format when new format not available', () => {
      const result = getCryptoData(legacyContactData);
      expect(result.eth).toBe('******************************************');
      expect(result.bsc).toBe('******************************************');
      expect(result.fil).toBe('f1234567890123456789012345678901234567890');
    });
  });

  describe('hasSocialData', () => {
    it('should return true when social data exists', () => {
      expect(hasSocialData(legacyContactData)).toBe(true);
      expect(hasSocialData(newFormatContactData)).toBe(true);
    });

    it('should return false when no social data exists', () => {
      expect(hasSocialData({ name: 'test' })).toBe(false);
    });
  });

  describe('hasCryptoData', () => {
    it('should return true when crypto data exists', () => {
      expect(hasCryptoData(legacyContactData)).toBe(true);
      expect(hasCryptoData(newFormatContactData)).toBe(true);
    });

    it('should return false when no crypto data exists', () => {
      expect(hasCryptoData({ name: 'test' })).toBe(false);
    });
  });

  describe('getSocialUrl', () => {
    it('should generate correct URLs for different platforms', () => {
      expect(getSocialUrl('twitter', 'testuser')).toBe('https://x.com/testuser');
      expect(getSocialUrl('telegram', 'testuser')).toBe('https://t.me/testuser');
      expect(getSocialUrl('instagram', 'testuser')).toBe('https://instagram.com/testuser');
    });

    it('should handle @ symbols', () => {
      expect(getSocialUrl('twitter', '@testuser')).toBe('https://x.com/testuser');
    });

    it('should handle full URLs', () => {
      expect(getSocialUrl('youtube', 'https://youtube.com/@testuser')).toBe('https://youtube.com/@testuser');
    });
  });

  describe('validateSocialHandle', () => {
    it('should validate Twitter handles correctly', () => {
      expect(validateSocialHandle('twitter', 'validuser')).toBeNull();
      expect(validateSocialHandle('twitter', 'a'.repeat(16))).toContain('1-15 characters');
    });

    it('should validate Telegram handles correctly', () => {
      expect(validateSocialHandle('telegram', 'validuser')).toBeNull();
      expect(validateSocialHandle('telegram', 'abc')).toContain('5-32 characters');
    });
  });

  describe('validateCryptoAddress', () => {
    it('should validate Ethereum addresses correctly', () => {
      expect(validateCryptoAddress('eth', '******************************************')).toBeNull();
      expect(validateCryptoAddress('eth', 'invalid')).toContain('0x followed by 40 hexadecimal');
    });

    it('should validate Bitcoin addresses correctly', () => {
      expect(validateCryptoAddress('btc', '**********************************')).toBeNull();
      expect(validateCryptoAddress('btc', 'invalid')).toContain('Invalid Bitcoin address');
    });
  });

  describe('prepareContactData', () => {
    it('should prepare data for database insertion', () => {
      const result = prepareContactData(legacyContactData);
      
      // Should have new format
      expect(result.social).toBeDefined();
      expect(result.crypto).toBeDefined();
      
      // Should not have legacy fields
      expect(result.twitter).toBeUndefined();
      expect(result.eth).toBeUndefined();
      
      // Should preserve other fields
      expect(result.name).toBe('test');
    });
  });
});
