import { SOCIAL_SLOTS, CRYPTO_SLOTS } from './config';

/**
 * Database utility functions for handling social and crypto data migration
 */

// Type definitions for the new structure
export interface SocialData {
  [key: string]: string;
}

export interface CryptoData {
  [key: string]: string;
}

// Type for contact data with new structure
export interface ContactData {
  // New fields
  social?: SocialData | null;
  crypto?: CryptoData | null;

  // Other fields
  [key: string]: any;
}

/**
 * Create empty social data structure
 */
export function createEmptySocialData(): SocialData {
  const social: SocialData = {};
  SOCIAL_SLOTS.forEach(platform => {
    social[platform] = '';
  });
  return social;
}

/**
 * Create empty crypto data structure
 */
export function createEmptyCryptoData(): CryptoData {
  const crypto: CryptoData = {};
  CRYPTO_SLOTS.forEach(currency => {
    crypto[currency] = '';
  });
  return crypto;
}

/**
 * Get social data from contact data
 */
export function getSocialData(data: ContactData): SocialData {
  if (data.social && typeof data.social === 'object') {
    return data.social as SocialData;
  }
  return {};
}

/**
 * Get crypto data from contact data
 */
export function getCryptoData(data: ContactData): CryptoData {
  if (data.crypto && typeof data.crypto === 'object') {
    return data.crypto as CryptoData;
  }
  return {};
}

/**
 * Prepare contact data for database insertion/update
 */
export function prepareContactData(data: ContactData): any {
  const social = getSocialData(data);
  const crypto = getCryptoData(data);

  // Create new data object
  const newData = { ...data };

  // Ensure social and crypto are properly formatted
  newData.social = Object.keys(social).some(key => social[key]?.trim()) ? social : null;
  newData.crypto = Object.keys(crypto).some(key => crypto[key]?.trim()) ? crypto : null;

  return newData;
}

/**
 * Check if contact has any social media data
 */
export function hasSocialData(data: ContactData): boolean {
  const social = getSocialData(data);
  return Object.values(social).some(value => value && value.trim());
}

/**
 * Check if contact has any crypto data
 */
export function hasCryptoData(data: ContactData): boolean {
  const crypto = getCryptoData(data);
  return Object.values(crypto).some(value => value && value.trim());
}

/**
 * Get social media URL for a platform and handle
 */
export function getSocialUrl(platform: string, handle: string): string {
  if (!handle || !handle.trim()) return '';
  
  const cleanHandle = handle.replace(/^@/, '');
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      return `https://x.com/${cleanHandle}`;
    case 'telegram':
      return `https://t.me/${cleanHandle}`;
    case 'instagram':
      return `https://instagram.com/${cleanHandle}`;
    case 'facebook':
      return handle.startsWith('http') ? handle : `https://facebook.com/${cleanHandle}`;
    case 'youtube':
      return handle.startsWith('http') ? handle : `https://youtube.com/@${cleanHandle}`;
    case 'discord':
      return handle; // Discord handles are typically just usernames
    default:
      return handle.startsWith('http') ? handle : `https://${handle}`;
  }
}

/**
 * Validate social media handle format
 */
export function validateSocialHandle(platform: string, handle: string): string | null {
  if (!handle || !handle.trim()) return null;
  
  const cleanHandle = handle.replace(/^@/, '').trim();
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      if (!/^[a-zA-Z0-9_]{1,15}$/.test(cleanHandle)) {
        return 'Twitter handle must be 1-15 characters, letters, numbers, and underscores only';
      }
      break;
    case 'telegram':
      if (!/^[a-zA-Z0-9_]{5,32}$/.test(cleanHandle)) {
        return 'Telegram handle must be 5-32 characters, letters, numbers, and underscores only';
      }
      break;
    case 'instagram':
      if (!/^[a-zA-Z0-9_.]{1,30}$/.test(cleanHandle)) {
        return 'Instagram handle must be 1-30 characters, letters, numbers, dots, and underscores only';
      }
      break;
  }
  
  return null;
}

/**
 * Validate crypto address format (basic validation)
 */
export function validateCryptoAddress(currency: string, address: string): string | null {
  if (!address || !address.trim()) return null;
  
  const cleanAddress = address.trim();
  
  switch (currency.toLowerCase()) {
    case 'eth':
    case 'bsc':
    case 'matic':
      if (!/^0x[a-fA-F0-9]{40}$/.test(cleanAddress)) {
        return `${currency.toUpperCase()} address must start with 0x followed by 40 hexadecimal characters`;
      }
      break;
    case 'btc':
      if (!/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(cleanAddress) && 
          !/^bc1[a-z0-9]{39,59}$/.test(cleanAddress)) {
        return 'Invalid Bitcoin address format';
      }
      break;
    case 'sol':
      if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(cleanAddress)) {
        return 'Invalid Solana address format';
      }
      break;
  }
  
  return null;
}
