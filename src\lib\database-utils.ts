import { SOCIAL_SLOTS, CRYPTO_SLOTS } from './config';

/**
 * Database utility functions for handling social and crypto data migration
 */

// Type definitions for the new structure
export interface SocialData {
  [key: string]: string;
}

export interface CryptoData {
  [key: string]: string;
}

// Type for legacy contact data (with individual columns)
export interface LegacyContactData {
  // Social media fields
  twitter?: string | null;
  telegram?: string | null;
  youtube?: string | null;
  instagram?: string | null;
  facebook?: string | null;
  
  // Crypto fields
  eth?: string | null;
  bsc?: string | null;
  matic?: string | null;
  btc?: string | null;
  fil?: string | null;
  sol?: string | null;
  
  // New fields
  social?: SocialData | null;
  crypto?: CryptoData | null;
  
  // Other fields
  [key: string]: any;
}

/**
 * Convert legacy individual social media columns to new social JSON structure
 */
export function migrateSocialData(data: LegacyContactData): SocialData {
  const social: SocialData = {};
  
  SOCIAL_SLOTS.forEach(platform => {
    const value = data[platform as keyof LegacyContactData];
    if (value && typeof value === 'string' && value.trim()) {
      social[platform] = value.trim();
    }
  });
  
  return social;
}

/**
 * Convert legacy individual crypto columns to new crypto JSON structure
 */
export function migrateCryptoData(data: LegacyContactData): CryptoData {
  const crypto: CryptoData = {};
  
  CRYPTO_SLOTS.forEach(currency => {
    const value = data[currency as keyof LegacyContactData];
    if (value && typeof value === 'string' && value.trim()) {
      crypto[currency] = value.trim();
    }
  });
  
  return crypto;
}

/**
 * Get social data from either new or legacy format
 */
export function getSocialData(data: LegacyContactData): SocialData {
  // If new format exists, use it
  if (data.social && typeof data.social === 'object') {
    return data.social as SocialData;
  }
  
  // Otherwise, migrate from legacy format
  return migrateSocialData(data);
}

/**
 * Get crypto data from either new or legacy format
 */
export function getCryptoData(data: LegacyContactData): CryptoData {
  // If new format exists, use it
  if (data.crypto && typeof data.crypto === 'object') {
    return data.crypto as CryptoData;
  }
  
  // Otherwise, migrate from legacy format
  return migrateCryptoData(data);
}

/**
 * Prepare contact data for database insertion/update with new structure
 */
export function prepareContactData(data: LegacyContactData): any {
  const social = getSocialData(data);
  const crypto = getCryptoData(data);
  
  // Create new data object without individual social/crypto columns
  const newData = { ...data };
  
  // Remove legacy individual columns
  SOCIAL_SLOTS.forEach(platform => {
    delete newData[platform];
  });
  
  CRYPTO_SLOTS.forEach(currency => {
    delete newData[currency];
  });
  
  // Add new JSON columns
  newData.social = Object.keys(social).length > 0 ? social : null;
  newData.crypto = Object.keys(crypto).length > 0 ? crypto : null;
  
  return newData;
}

/**
 * Check if contact has any social media data
 */
export function hasSocialData(data: LegacyContactData): boolean {
  const social = getSocialData(data);
  return Object.values(social).some(value => value && value.trim());
}

/**
 * Check if contact has any crypto data
 */
export function hasCryptoData(data: LegacyContactData): boolean {
  const crypto = getCryptoData(data);
  return Object.values(crypto).some(value => value && value.trim());
}

/**
 * Get social media URL for a platform and handle
 */
export function getSocialUrl(platform: string, handle: string): string {
  if (!handle || !handle.trim()) return '';
  
  const cleanHandle = handle.replace(/^@/, '');
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      return `https://x.com/${cleanHandle}`;
    case 'telegram':
      return `https://t.me/${cleanHandle}`;
    case 'instagram':
      return `https://instagram.com/${cleanHandle}`;
    case 'facebook':
      return handle.startsWith('http') ? handle : `https://facebook.com/${cleanHandle}`;
    case 'youtube':
      return handle.startsWith('http') ? handle : `https://youtube.com/@${cleanHandle}`;
    case 'discord':
      return handle; // Discord handles are typically just usernames
    default:
      return handle.startsWith('http') ? handle : `https://${handle}`;
  }
}

/**
 * Validate social media handle format
 */
export function validateSocialHandle(platform: string, handle: string): string | null {
  if (!handle || !handle.trim()) return null;
  
  const cleanHandle = handle.replace(/^@/, '').trim();
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      if (!/^[a-zA-Z0-9_]{1,15}$/.test(cleanHandle)) {
        return 'Twitter handle must be 1-15 characters, letters, numbers, and underscores only';
      }
      break;
    case 'telegram':
      if (!/^[a-zA-Z0-9_]{5,32}$/.test(cleanHandle)) {
        return 'Telegram handle must be 5-32 characters, letters, numbers, and underscores only';
      }
      break;
    case 'instagram':
      if (!/^[a-zA-Z0-9_.]{1,30}$/.test(cleanHandle)) {
        return 'Instagram handle must be 1-30 characters, letters, numbers, dots, and underscores only';
      }
      break;
  }
  
  return null;
}

/**
 * Validate crypto address format (basic validation)
 */
export function validateCryptoAddress(currency: string, address: string): string | null {
  if (!address || !address.trim()) return null;
  
  const cleanAddress = address.trim();
  
  switch (currency.toLowerCase()) {
    case 'eth':
    case 'bsc':
    case 'matic':
      if (!/^0x[a-fA-F0-9]{40}$/.test(cleanAddress)) {
        return `${currency.toUpperCase()} address must start with 0x followed by 40 hexadecimal characters`;
      }
      break;
    case 'btc':
      if (!/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(cleanAddress) && 
          !/^bc1[a-z0-9]{39,59}$/.test(cleanAddress)) {
        return 'Invalid Bitcoin address format';
      }
      break;
    case 'sol':
      if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(cleanAddress)) {
        return 'Invalid Solana address format';
      }
      break;
  }
  
  return null;
}
